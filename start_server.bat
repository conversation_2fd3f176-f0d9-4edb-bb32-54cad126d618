@echo off
echo Starting MCP Server Qdrant...
echo.

REM Activate virtual environment
call .venv\Scripts\activate.bat

REM Set default environment variables if not already set
if not defined QDRANT_URL set QDRANT_URL=:memory:
if not defined COLLECTION_NAME set COLLECTION_NAME=test
if not defined EMBEDDING_MODEL set EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

echo Configuration:
echo - Qdrant URL: %QDRANT_URL%
echo - Collection: %COLLECTION_NAME%
echo - Embedding Model: %EMBEDDING_MODEL%
echo.

REM Set Python path and run the server
set PYTHONPATH=src
python start_server.py --transport sse

pause
