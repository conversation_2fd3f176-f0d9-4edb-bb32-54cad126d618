#!/usr/bin/env python3
"""
Test script to verify MCP tools are working
"""
import os
import sys
import json
import subprocess

# Set environment variables
os.environ['QDRANT_URL'] = 'http://localhost:6333'
os.environ['COLLECTION_NAME'] = 'mcp-memories'
os.environ['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'

def test_mcp_tools():
    """Test MCP tools via the command line"""
    
    # Test 1: List available tools
    print("Testing MCP Server Tools...")
    print("=" * 50)
    
    try:
        # Start the server and test tools
        venv_python = ".venv/Scripts/python.exe"
        
        # Test if we can import the server
        test_import = subprocess.run([
            venv_python, "-c",
            "import mcp_server_qdrant.server; print('Server module imported successfully')"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if test_import.returncode == 0:
            print("✓ " + test_import.stdout.strip())
        else:
            print(f"✗ Import failed: {test_import.stderr}")
            return False
            
        # Test Qdrant connection
        test_qdrant = subprocess.run([
            venv_python, "-c", 
            """
import os
from mcp_server_qdrant.qdrant import QdrantConnector
from mcp_server_qdrant.settings import QdrantSettings, EmbeddingProviderSettings

try:
    settings = QdrantSettings()
    embedding_settings = EmbeddingProviderSettings()
    
    connector = QdrantConnector(
        qdrant_url=settings.location,
        qdrant_api_key=settings.api_key,
        collection_name=settings.collection_name,
        embedding_provider=embedding_settings
    )
    print('Qdrant connection test passed')
except Exception as e:
    print(f'✗ Qdrant connection failed: {e}')
    """
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if test_qdrant.returncode == 0:
            print("✓ " + test_qdrant.stdout.strip())
        else:
            print(f"✗ Qdrant test failed: {test_qdrant.stderr}")
            
        print("\n" + "=" * 50)
        print("✅ MCP Server is ready!")
        print("\nAvailable tools:")
        print("1. qdrant-store - Store information in Qdrant")
        print("2. qdrant-find - Search for information in Qdrant")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_mcp_tools()
    sys.exit(0 if success else 1)
