# MCP Server Qdrant - Current Tools & Codebase Indexing Solution

## Current Server Tools & Capabilities

### Core MCP Server Tools
The `mcp-server-qdrant` provides two primary tools for vector-based semantic search:

#### 1. `qdrant-store` Tool
- **Purpose**: Store information in Qdrant vector database
- **Parameters**:
  - `information`: Text content to store
  - `collection_name`: Target collection name
  - `metadata`: Optional metadata (JSON object)
- **Functionality**: Embeds text using FastEmbed and stores in Qdrant with metadata

#### 2. `qdrant-find` Tool  
- **Purpose**: Search and retrieve relevant information from Qdrant
- **Parameters**:
  - `query`: Natural language search query
  - `collection_name`: Collection to search in
  - `limit`: Number of results to return (default: 10)
  - `filters`: Optional filtering criteria
- **Functionality**: Performs semantic similarity search using vector embeddings

### Technical Architecture

#### Embedding Provider
- **Current**: FastEmbed with `sentence-transformers/all-MiniLM-L6-v2`
- **Vector Size**: 384 dimensions
- **Distance Metric**: Cosine similarity
- **Extensible**: Factory pattern allows adding new embedding providers

#### Qdrant Integration
- **Client**: AsyncQdrantClient for high-performance operations
- **Storage**: Supports both local and remote Qdrant instances
- **Collections**: Auto-creates collections with proper vector configuration
- **Metadata**: Full metadata support with filterable fields

#### Configuration Options
- **Transport**: stdio, sse, streamable-http
- **Environment Variables**: 
  - `QDRANT_URL`, `QDRANT_API_KEY`
  - `COLLECTION_NAME`, `EMBEDDING_MODEL`
  - `QDRANT_READ_ONLY`, `QDRANT_SEARCH_LIMIT`
- **Filterable Fields**: Configurable metadata filtering

## Solution for Codebase Indexing Issue

### Problem Analysis
The GitHub issue #411 requests codebase indexing similar to Cursor for efficient code analysis and grep searching. The current MCP server provides the foundation but needs enhancement for code-specific use cases.

### Proposed Solution: Enhanced Code Indexing MCP Server

#### 1. Code-Specific Enhancements

```python
# Enhanced tool descriptions for code indexing
TOOL_STORE_DESCRIPTION = """
Store code snippets, functions, classes, or documentation with semantic understanding.
- 'information': Natural language description of code functionality
- 'metadata': Include 'code', 'file_path', 'language', 'function_name', 'class_name'
"""

TOOL_FIND_DESCRIPTION = """
Search codebase using natural language queries. Find:
- Functions by functionality description
- Similar code patterns
- Usage examples
- Documentation and comments
- Cross-references and dependencies
"""
```

#### 2. Automated Codebase Ingestion Script

```python
# codebase_indexer.py - New component to add
import os
import ast
from pathlib import Path
from typing import Dict, List

class CodebaseIndexer:
    def __init__(self, mcp_server):
        self.mcp_server = mcp_server
        
    async def index_repository(self, repo_path: str, collection_name: str):
        """Index entire codebase with semantic understanding"""
        for file_path in self._get_code_files(repo_path):
            await self._index_file(file_path, collection_name)
    
    async def _index_file(self, file_path: Path, collection_name: str):
        """Index individual file with function/class level granularity"""
        # Parse code structure
        # Extract functions, classes, docstrings
        # Store with rich metadata
        pass
```

#### 3. Enhanced Metadata Schema

```python
# Enhanced metadata for code indexing
code_metadata = {
    "file_path": "src/module/file.py",
    "language": "python",
    "type": "function|class|module|comment",
    "name": "function_name",
    "line_start": 10,
    "line_end": 25,
    "dependencies": ["import1", "import2"],
    "complexity": "low|medium|high",
    "last_modified": "2024-01-26",
    "git_hash": "abc123"
}
```

#### 4. Implementation Strategy

**Phase 1: Core Enhancement**
- Add code parsing capabilities (AST for Python, tree-sitter for multi-language)
- Enhance metadata schema for code-specific fields
- Create automated indexing scripts

**Phase 2: Advanced Features**
- Cross-reference detection
- Dependency mapping
- Change impact analysis
- Code similarity detection

**Phase 3: Integration**
- Git hooks for automatic re-indexing
- IDE plugins for real-time updates
- Performance optimization for large codebases

### Benefits Over Traditional Grep

1. **Semantic Understanding**: Find code by functionality, not just keywords
2. **Context Awareness**: Understand relationships between code components
3. **Natural Language Queries**: "Find functions that handle file uploads"
4. **Cross-Language Support**: Unified search across different programming languages
5. **Metadata Filtering**: Filter by file type, complexity, recency, etc.

### Integration with Existing Tools

The current MCP server already provides the foundation:
- ✅ Vector storage and retrieval
- ✅ Metadata filtering
- ✅ Configurable embedding models
- ✅ Multiple transport protocols
- ✅ Environment-based configuration

**Required Additions**:
- Code parsing and AST analysis
- Automated indexing workflows
- Enhanced metadata schemas
- Performance optimizations for large codebases

This solution addresses the core need while leveraging the existing robust MCP server infrastructure.
