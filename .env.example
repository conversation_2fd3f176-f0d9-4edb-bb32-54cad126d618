# Qdrant MCP Server Configuration
# Copy this file to .env and configure your settings

# Qdrant Connection Settings
# Option 1: Remote Qdrant instance (cloud or self-hosted)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_api_key_here

# Option 2: Local Qdrant database (alternative to QDRANT_URL)
# QDRANT_LOCAL_PATH=/path/to/local/qdrant/database

# Collection Settings
COLLECTION_NAME=my-collection

# Embedding Settings
EMBEDDING_PROVIDER=fastembed
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Tool Descriptions (optional customization)
# TOOL_STORE_DESCRIPTION="Store some information in the Qdrant database"
# TOOL_FIND_DESCRIPTION="Retrieve relevant information from the Qdrant database"

# FastMCP Settings (optional)
FASTMCP_DEBUG=false
FASTMCP_LOG_LEVEL=INFO
FASTMCP_HOST=127.0.0.1
FASTMCP_PORT=8000

# Additional Qdrant Settings (optional)
# QDRANT_SEARCH_LIMIT=10
# QDRANT_READ_ONLY=false
# QDRANT_ALLOW_ARBITRARY_FILTER=false
