{"name": "qdrant-mcp-server", "version": "1.0.0", "description": "MCP Server for Qdrant vector database", "server": {"executable": "python", "args": ["C:/Users/<USER>/tools/mcp-server-qdrant/run_server.py"], "env": {"QDRANT_URL": "http://localhost:6333", "COLLECTION_NAME": "mcp-memories", "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2", "PYTHONPATH": "src"}, "cwd": "C:/Users/<USER>/tools/mcp-server-qdrant"}, "transport": {"type": "stdio"}, "tools": [{"name": "qdrant-store", "description": "Store information in the Qdrant vector database", "inputSchema": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to store"}, "metadata": {"type": "object", "description": "Optional metadata to associate with the content"}}, "required": ["content"]}}, {"name": "qdrant-find", "description": "Search and retrieve relevant information from Qdrant", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query"}, "limit": {"type": "integer", "description": "Maximum number of results to return", "default": 10}}, "required": ["query"]}}]}