#!/usr/bin/env python3
"""
Startup script for the MCP Server Qdrant
"""
import os
import sys

# Add src to path
sys.path.insert(0, 'src')

# Set default environment variables if not set
if 'QDRANT_URL' not in os.environ:
    os.environ['QDRANT_URL'] = ':memory:'

if 'COLLECTION_NAME' not in os.environ:
    os.environ['COLLECTION_NAME'] = 'test'

if 'EMBEDDING_MODEL' not in os.environ:
    os.environ['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'

# Import and run the main function
from mcp_server_qdrant.main import main

if __name__ == "__main__":
    print("Starting MCP Server Qdrant...")
    print(f"Qdrant URL: {os.environ.get('QDRANT_URL')}")
    print(f"Collection: {os.environ.get('COLLECTION_NAME')}")
    print(f"Embedding Model: {os.environ.get('EMBEDDING_MODEL')}")
    print("-" * 50)
    main()
