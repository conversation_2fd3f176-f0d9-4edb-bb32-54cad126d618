#!/usr/bin/env python3
"""
Simple script to run the MCP Server Qdrant
"""
import os
import sys
import subprocess

def main():
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = 'src'
    
    # Set default values if not provided
    if 'QDRANT_URL' not in env:
        env['QDRANT_URL'] = ':memory:'
    if 'COLLECTION_NAME' not in env:
        env['COLLECTION_NAME'] = 'test'
    if 'EMBEDDING_MODEL' not in env:
        env['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'
    
    print("MCP Server Qdrant Configuration:")
    print(f"- Qdrant URL: {env['QDRANT_URL']}")
    print(f"- Collection: {env['COLLECTION_NAME']}")
    print(f"- Embedding Model: {env['EMBEDDING_MODEL']}")
    print("-" * 50)
    
    # Get transport from command line args
    transport = 'stdio'
    if len(sys.argv) > 1 and sys.argv[1] == '--transport':
        if len(sys.argv) > 2:
            transport = sys.argv[2]
    
    print(f"Starting server with {transport} transport...")
    
    # Run the server
    cmd = [sys.executable, '-m', 'mcp_server_qdrant.main', '--transport', transport]
    
    try:
        subprocess.run(cmd, env=env, cwd=os.getcwd())
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    except Exception as e:
        print(f"Error running server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
