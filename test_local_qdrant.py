#!/usr/bin/env python3
"""
Test script to verify MCP server works with local Qdrant
"""
import os
import sys
import json
import subprocess
import tempfile
import time

def test_local_qdrant():
    """Test MCP server with local Qdrant storage"""
    
    print("Testing MCP Server with Local Qdrant...")
    print("=" * 50)
    
    # Create a temporary directory for Qdrant data
    temp_dir = tempfile.mkdtemp(prefix="qdrant_test_")
    print(f"Using temporary Qdrant data directory: {temp_dir}")
    
    # Set environment variables for local mode
    env = os.environ.copy()
    env['QDRANT_LOCAL_PATH'] = temp_dir
    env['COLLECTION_NAME'] = 'test-collection'
    env['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'
    
    # Remove QDRANT_URL if it exists
    env.pop('QDRANT_URL', None)
    
    try:
        # Test 1: Start server and test basic functionality
        print("\n1. Testing server startup...")
        
        venv_exe = ".venv/Scripts/mcp-server-qdrant.exe"
        
        # Start server in background
        server_process = subprocess.Popen([
            venv_exe, "--transport", "stdio"
        ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
           env=env, text=True, cwd=os.getcwd())
        
        # Give server time to start
        time.sleep(3)
        
        # Test 2: Send MCP initialization
        print("2. Testing MCP protocol initialization...")
        
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send initialization
        server_process.stdin.write(json.dumps(init_request) + "\n")
        server_process.stdin.flush()
        
        # Read response
        response_line = server_process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line.strip())
                if response.get("result"):
                    print("✓ MCP initialization successful")
                    print(f"  Server capabilities: {list(response['result'].get('capabilities', {}).keys())}")
                else:
                    print(f"✗ MCP initialization failed: {response}")
                    return False
            except json.JSONDecodeError as e:
                print(f"✗ Invalid JSON response: {e}")
                print(f"  Raw response: {response_line}")
                return False
        else:
            print("✗ No response from server")
            return False
        
        # Test 3: List available tools
        print("3. Testing tools listing...")
        
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        server_process.stdin.write(json.dumps(tools_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line.strip())
                if response.get("result") and "tools" in response["result"]:
                    tools = response["result"]["tools"]
                    print(f"✓ Found {len(tools)} tools:")
                    for tool in tools:
                        print(f"  - {tool['name']}: {tool.get('description', 'No description')}")
                else:
                    print(f"✗ Tools listing failed: {response}")
                    return False
            except json.JSONDecodeError as e:
                print(f"✗ Invalid JSON response: {e}")
                return False
        
        # Test 4: Test qdrant-store tool
        print("4. Testing qdrant-store tool...")
        
        store_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "qdrant-store",
                "arguments": {
                    "information": "This is a test memory for the MCP server verification",
                    "metadata": {"test": True, "timestamp": "2025-01-27"}
                }
            }
        }
        
        server_process.stdin.write(json.dumps(store_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line.strip())
                if response.get("result"):
                    print("✓ Successfully stored test data")
                else:
                    print(f"✗ Store operation failed: {response}")
                    return False
            except json.JSONDecodeError as e:
                print(f"✗ Invalid JSON response: {e}")
                return False
        
        # Test 5: Test qdrant-find tool
        print("5. Testing qdrant-find tool...")
        
        find_request = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "qdrant-find",
                "arguments": {
                    "query": "test memory verification"
                }
            }
        }
        
        server_process.stdin.write(json.dumps(find_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line.strip())
                if response.get("result"):
                    print("✓ Successfully retrieved test data")
                    print(f"  Found results: {len(response['result'].get('content', []))}")
                else:
                    print(f"✗ Find operation failed: {response}")
                    return False
            except json.JSONDecodeError as e:
                print(f"✗ Invalid JSON response: {e}")
                return False
        
        print("\n" + "=" * 50)
        print("✅ ALL TESTS PASSED!")
        print("✅ MCP Server is fully functional with local Qdrant")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False
    finally:
        # Clean up
        if 'server_process' in locals():
            server_process.terminate()
            server_process.wait()
        
        # Clean up temp directory
        import shutil
        try:
            shutil.rmtree(temp_dir)
            print(f"Cleaned up temporary directory: {temp_dir}")
        except:
            pass

if __name__ == "__main__":
    success = test_local_qdrant()
    sys.exit(0 if success else 1)
