#!/usr/bin/env python3
"""
Async test to verify Qdrant local mode works
"""
import os
import tempfile
import shutil
import asyncio

async def test_async():
    # Create temp directory
    temp_dir = tempfile.mkdtemp(prefix="qdrant_test_")
    print(f"Testing with local Qdrant at: {temp_dir}")
    
    try:
        # Set environment
        os.environ['QDRANT_LOCAL_PATH'] = temp_dir
        os.environ['COLLECTION_NAME'] = 'test-collection'
        os.environ['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'
        os.environ.pop('QDRANT_URL', None)
        
        # Import and test the server components
        from mcp_server_qdrant.qdrant import QdrantConnector, Entry
        from mcp_server_qdrant.settings import QdrantSettings, EmbeddingProviderSettings
        from mcp_server_qdrant.embeddings.factory import create_embedding_provider

        print("✓ Imported server components")

        # Create settings
        settings = QdrantSettings()
        embedding_settings = EmbeddingProviderSettings()

        print(f"✓ Settings created - Local path: {settings.local_path}")

        # Create embedding provider
        embedding_provider = create_embedding_provider(embedding_settings)
        print("✓ Embedding provider created")

        # Create connector
        connector = QdrantConnector(
            qdrant_url=settings.location,
            qdrant_api_key=settings.api_key,
            collection_name=settings.collection_name,
            embedding_provider=embedding_provider,
            qdrant_local_path=settings.local_path
        )
        
        print("✓ Connector created")
        
        # Test store operation
        test_entry = Entry(
            content="This is a test memory for verification",
            metadata={"test": True, "source": "verification"}
        )
        
        await connector.store(test_entry)
        print("✓ Store operation successful")
        
        # Test search operation
        results = await connector.search("test memory verification")
        
        print(f"✓ Search operation successful: Found {len(results)} results")
        
        if results:
            print(f"  First result content: {results[0].content[:100]}...")
            print(f"  First result metadata: {results[0].metadata}")
        
        print("\n✅ ALL TESTS PASSED!")
        print("✅ Local Qdrant mode is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        try:
            shutil.rmtree(temp_dir)
            print(f"Cleaned up: {temp_dir}")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_async())
