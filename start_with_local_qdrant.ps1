# PowerShell script to start MCP Server with local Qdrant
Write-Host "Starting MCP Server Qdrant with local Qdrant server..." -ForegroundColor Green
Write-Host ""

# Set environment variables for local Qdrant
$env:QDRANT_URL = "http://localhost:6333"
$env:COLLECTION_NAME = "mcp-memories"
$env:EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
$env:PYTHONPATH = "src"
$env:FASTMCP_PORT = "8000"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "- Qdrant URL: $env:QDRANT_URL"
Write-Host "- Collection: $env:COLLECTION_NAME"
Write-Host "- Embedding Model: $env:EMBEDDING_MODEL"
Write-Host "- Server Port: $env:FASTMCP_PORT"
Write-Host ""

Write-Host "Starting server with SSE transport on port 8000..." -ForegroundColor Cyan
Write-Host "You can access the server at: http://localhost:8000" -ForegroundColor Green
Write-Host ""

# Run the server
& ".venv/Scripts/python.exe" "run_server.py" "--transport" "sse"
