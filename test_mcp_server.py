#!/usr/bin/env python3
"""
Test the actual MCP server with local Qdrant
"""
import os
import json
import subprocess
import time
import tempfile

def test_mcp_server():
    """Test the MCP server end-to-end"""
    
    # Create persistent directory for this test
    test_dir = os.path.join(os.getcwd(), "test_qdrant_data")
    os.makedirs(test_dir, exist_ok=True)
    
    print(f"Testing MCP Server with Qdrant data at: {test_dir}")
    print("=" * 60)
    
    # Set environment variables
    env = os.environ.copy()
    env['QDRANT_LOCAL_PATH'] = test_dir
    env['COLLECTION_NAME'] = 'mcp-test-collection'
    env['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'
    
    # Remove QDRANT_URL if it exists
    env.pop('QDRANT_URL', None)
    
    try:
        # Start the MCP server
        print("1. Starting MCP server...")
        
        server_process = subprocess.Popen([
            ".venv/Scripts/mcp-server-qdrant.exe", "--transport", "stdio"
        ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
           env=env, text=True, cwd=os.getcwd())
        
        time.sleep(2)  # Give server time to start
        
        # Test MCP protocol
        print("2. Testing MCP initialization...")
        
        # Initialize
        init_msg = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "test-client", "version": "1.0.0"}
            }
        }
        
        server_process.stdin.write(json.dumps(init_msg) + "\n")
        server_process.stdin.flush()
        
        response = server_process.stdout.readline()
        init_response = json.loads(response.strip())
        
        if "result" in init_response:
            print("✓ MCP initialization successful")
            capabilities = init_response["result"].get("capabilities", {})
            print(f"  Server capabilities: {list(capabilities.keys())}")
        else:
            print(f"✗ Initialization failed: {init_response}")
            return False
        
        # Test tools listing
        print("3. Testing tools listing...")
        
        tools_msg = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        server_process.stdin.write(json.dumps(tools_msg) + "\n")
        server_process.stdin.flush()
        
        response = server_process.stdout.readline()
        tools_response = json.loads(response.strip())
        
        if "result" in tools_response and "tools" in tools_response["result"]:
            tools = tools_response["result"]["tools"]
            print(f"✓ Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool.get('description', 'No description')[:50]}...")
        else:
            print(f"✗ Tools listing failed: {tools_response}")
            return False
        
        # Test qdrant-store
        print("4. Testing qdrant-store tool...")
        
        store_msg = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "qdrant-store",
                "arguments": {
                    "information": "This is a comprehensive test of the MCP server with local Qdrant storage. The test verifies that we can store and retrieve information successfully.",
                    "metadata": {
                        "test_type": "end_to_end",
                        "timestamp": "2025-01-27",
                        "verified": True
                    }
                }
            }
        }
        
        server_process.stdin.write(json.dumps(store_msg) + "\n")
        server_process.stdin.flush()
        
        response = server_process.stdout.readline()
        store_response = json.loads(response.strip())
        
        if "result" in store_response:
            print("✓ Store operation successful")
            print(f"  Response: {store_response['result']}")
        else:
            print(f"✗ Store operation failed: {store_response}")
            return False
        
        # Test qdrant-find
        print("5. Testing qdrant-find tool...")
        
        find_msg = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "qdrant-find",
                "arguments": {
                    "query": "comprehensive test MCP server local Qdrant"
                }
            }
        }
        
        server_process.stdin.write(json.dumps(find_msg) + "\n")
        server_process.stdin.flush()
        
        response = server_process.stdout.readline()
        find_response = json.loads(response.strip())
        
        if "result" in find_response:
            print("✓ Find operation successful")
            result_content = find_response["result"]
            if isinstance(result_content, list) and len(result_content) > 0:
                print(f"  Found {len(result_content)} results")
                print(f"  First result preview: {str(result_content[0])[:100]}...")
            else:
                print(f"  Result: {result_content}")
        else:
            print(f"✗ Find operation failed: {find_response}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ALL MCP SERVER TESTS PASSED!")
        print("✅ The MCP server is fully functional with local Qdrant")
        print("✅ Ready for Claude Desktop integration!")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        if 'server_process' in locals():
            server_process.terminate()
            server_process.wait()
        
        print(f"\nTest data preserved at: {test_dir}")
        print("You can delete this directory if you don't need the test data.")

if __name__ == "__main__":
    success = test_mcp_server()
    exit(0 if success else 1)
