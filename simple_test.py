#!/usr/bin/env python3
"""
Simple test to verify Qdrant local mode works
"""
import os
import tempfile
import shutil

def test_simple():
    # Create temp directory
    temp_dir = tempfile.mkdtemp(prefix="qdrant_test_")
    print(f"Testing with local Qdrant at: {temp_dir}")
    
    try:
        # Set environment
        os.environ['QDRANT_LOCAL_PATH'] = temp_dir
        os.environ['COLLECTION_NAME'] = 'test-collection'
        os.environ['EMBEDDING_MODEL'] = 'sentence-transformers/all-MiniLM-L6-v2'
        os.environ.pop('QDRANT_URL', None)
        
        # Import and test the server components
        from mcp_server_qdrant.qdrant import QdrantConnector
        from mcp_server_qdrant.settings import QdrantSettings, EmbeddingProviderSettings
        
        print("✓ Imported server components")
        
        # Create settings
        settings = QdrantSettings()
        embedding_settings = EmbeddingProviderSettings()
        
        print(f"✓ Settings created - Local path: {settings.local_path}")
        
        # Create connector
        connector = QdrantConnector(
            qdrant_url=settings.location,
            qdrant_api_key=settings.api_key,
            collection_name=settings.collection_name,
            embedding_provider=embedding_settings
        )
        
        print("✓ Connector created")
        
        # Test store operation
        result = connector.store_information(
            "This is a test memory for verification",
            {"test": True, "source": "verification"}
        )
        
        print(f"✓ Store operation successful: {result}")
        
        # Test find operation
        results = connector.find_information("test memory verification")
        
        print(f"✓ Find operation successful: Found {len(results)} results")
        
        if results:
            print(f"  First result: {results[0][:100]}...")
        
        print("\n✅ ALL TESTS PASSED!")
        print("✅ Local Qdrant mode is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        try:
            shutil.rmtree(temp_dir)
            print(f"Cleaned up: {temp_dir}")
        except:
            pass

if __name__ == "__main__":
    test_simple()
